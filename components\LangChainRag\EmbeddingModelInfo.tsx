"use client"

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Info, 
  ChevronDown, 
  ChevronUp, 
  Zap, 
  Target, 
  Globe,
  CheckCircle,
  Star
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface EmbeddingModelInfoProps {
  className?: string;
}

const EmbeddingModelInfo: React.FC<EmbeddingModelInfoProps> = ({ className }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const embeddingModels = [
    {
      name: "Cohere embed-english-v3.0",
      provider: "Cohere",
      status: "current",
      description: "Latest Cohere embedding model with excellent semantic understanding",
      pros: [
        "Best accuracy for English text",
        "Optimized for RAG applications", 
        "Strong performance on business documents",
        "Good handling of technical terminology"
      ],
      cons: [
        "English-only",
        "Requires API key"
      ],
      useCase: "Recommended for most business and technical documents",
      performance: "Excellent",
      color: "bg-green-100 text-green-800 border-green-200"
    },
    {
      name: "OpenAI text-embedding-3-large",
      provider: "OpenAI", 
      status: "alternative",
      description: "OpenAI's latest large embedding model with high dimensionality",
      pros: [
        "Very high accuracy",
        "Multilingual support",
        "Large context window",
        "Strong on diverse content types"
      ],
      cons: [
        "More expensive",
        "Requires OpenAI API key",
        "Higher latency"
      ],
      useCase: "Best for multilingual content or maximum accuracy",
      performance: "Excellent",
      color: "bg-blue-100 text-blue-800 border-blue-200"
    },
    {
      name: "Sentence Transformers",
      provider: "Open Source",
      status: "local",
      description: "Free, open-source embedding models that run locally",
      pros: [
        "Free to use",
        "No API limits",
        "Privacy-focused",
        "Many model variants"
      ],
      cons: [
        "Lower accuracy than commercial models",
        "Requires local compute",
        "More setup complexity"
      ],
      useCase: "Good for privacy-sensitive data or budget constraints",
      performance: "Good",
      color: "bg-purple-100 text-purple-800 border-purple-200"
    }
  ];

  return (
    <Card className={cn("border-l-4 border-l-blue-500", className)}>
      <CardHeader className="pb-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full justify-between p-0 h-auto font-medium"
        >
          <div className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            Embedding Model Guide
          </div>
          {isExpanded ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </Button>
      </CardHeader>
      
      {isExpanded && (
        <CardContent className="pt-0 space-y-4">
          <div className="text-sm text-muted-foreground">
            <p className="mb-3">
              Embedding models convert your text into numerical vectors for semantic search. 
              The quality of embeddings directly impacts answer accuracy.
            </p>
          </div>
          
          <div className="space-y-3">
            {embeddingModels.map((model, index) => (
              <div key={index} className="border rounded-lg p-3">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="text-sm font-medium">{model.name}</h4>
                      <Badge 
                        variant="outline" 
                        className={cn("text-xs", model.color)}
                      >
                        {model.status === 'current' && <CheckCircle className="h-3 w-3 mr-1" />}
                        {model.status === 'current' ? 'Current' : 
                         model.status === 'alternative' ? 'Alternative' : 'Local'}
                      </Badge>
                      {model.status === 'current' && (
                        <Star className="h-3 w-3 text-yellow-500" />
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground mb-2">
                      {model.description}
                    </p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
                  <div>
                    <h5 className="font-medium text-green-700 mb-1">Pros:</h5>
                    <ul className="space-y-1">
                      {model.pros.map((pro, i) => (
                        <li key={i} className="flex items-start gap-1">
                          <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                          <span>{pro}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h5 className="font-medium text-amber-700 mb-1">Considerations:</h5>
                    <ul className="space-y-1">
                      {model.cons.map((con, i) => (
                        <li key={i} className="flex items-start gap-1">
                          <Info className="h-3 w-3 text-amber-500 mt-0.5 flex-shrink-0" />
                          <span>{con}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                
                <div className="mt-3 pt-3 border-t">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">
                      <strong>Best for:</strong> {model.useCase}
                    </span>
                    <div className="flex items-center gap-1">
                      <Target className="h-3 w-3" />
                      <span className="font-medium">{model.performance}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="bg-blue-50 dark:bg-blue-950/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800">
            <div className="flex items-start gap-2">
              <Star className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                  Current Recommendation
                </h4>
                <p className="text-blue-800 dark:text-blue-200 text-xs">
                  We're currently using <strong>Cohere embed-english-v3.0</strong> which provides 
                  the best balance of accuracy, speed, and cost for most business use cases. 
                  This model excels at understanding business terminology and technical documents.
                </p>
              </div>
            </div>
          </div>
          
          <div className="text-xs text-muted-foreground">
            <p>
              <strong>Note:</strong> All embeddings are stored in Pinecone vector database with 
              proper metadata for efficient retrieval and filtering.
            </p>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default EmbeddingModelInfo;
