import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { CohereEmbeddings } from '@langchain/cohere';
import { PineconeStore } from '@langchain/pinecone';
import { Document } from '@langchain/core/documents';
import { BM25Retriever } from '@langchain/community/retrievers/bm25';
import { ChatCohere } from '@langchain/cohere';
import { ChatTogetherAI } from '@langchain/community/chat_models/togetherai';

// Define interfaces for better type safety
interface RequestBody {
  message: string;
  history: Array<{ role: string; content: string }>;
  datasets: string[];
  pdfs: string[];
  deepResearch?: boolean;
  maxIterations?: number;
  model?: string;
  searchStrategy?: string;
  retrievalK?: number;
  namespace?: string;
  useNamespaceOnly?: boolean;
}

interface ChatResponse {
  success: boolean;
  content?: string;
  model?: string;
  provider?: string;
  sourceDocuments?: Array<{
    content: string;
    metadata: DocumentMetadata;
    index: number;
  }>;
  error?: string;
}

interface DocumentMetadata {
  source: string;
  datasetId?: string;
  pdfId?: string;
  [key: string]: any;
}

interface ScoredDocument {
  doc: Document;
  score: number;
}

interface HybridRetriever {
  getRelevantDocuments: (query: string) => Promise<Document[]>;
}

import { getModelProvider, getModelId } from './utils';

// Initialize Pinecone client
const getPineconeClient = (): Pinecone => {
  const apiKey = process.env.PINECONE_API_KEY;

  if (!apiKey) {
    throw new Error('PINECONE_API_KEY is not defined in environment variables');
  }

  return new Pinecone({
    apiKey,
  });
};

// Initialize Cohere embeddings with settings to match llama-text-embed-v2
const getEmbeddings = (): CohereEmbeddings => {
  const apiKey = process.env.COHERE_API_KEY;

  if (!apiKey) {
    throw new Error('COHERE_API_KEY is not defined in environment variables');
  }

  return new CohereEmbeddings({
    apiKey,
    model: 'embed-english-v3.0', // Using Cohere's latest embedding model
    inputType: 'search_query', // For query embeddings
    // @ts-ignore - truncate is a valid parameter but not in the type definitions
    truncate: 'END', // Truncate long texts from the end
    embeddingFormat: 'float' // Use float format for better precision
  });
};

// Get LLM based on selected model
const getLLM = (model: string): ChatCohere | ChatTogetherAI => {
  const provider = getModelProvider(model);
  const modelId = getModelId(model, provider);

  if (provider === 'cohere') {
    const apiKey = process.env.COHERE_API_KEY;
    if (!apiKey) {
      throw new Error('COHERE_API_KEY is not defined in environment variables');
    }

    return new ChatCohere({
      apiKey,
      model: modelId,
      temperature: 0.3,
      // @ts-ignore - maxTokens is a valid parameter but not in the type definitions
      maxTokens: 2000, // Increase output token limit
    });
  } else {
    const apiKey = process.env.TOGETHER_API_KEY;
    if (!apiKey) {
      throw new Error('TOGETHER_API_KEY is not defined in environment variables');
    }

    return new ChatTogetherAI({
      apiKey,
      modelName: modelId,
      temperature: 0.3,
      // @ts-ignore - maxTokens is a valid parameter but not in the type definitions
      maxTokens: 2000, // Increase output token limit
    });
  }
};

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body: RequestBody = await req.json();
    const {
      message,
      history = [],
      datasets = [],
      pdfs = [],
      deepResearch = false,
      maxIterations = 3,
      model = 'command-r-plus',
      searchStrategy = 'hybrid',
      retrievalK = 10,
      namespace = 'adeloop', // Use adeloop namespace directly
      useNamespaceOnly = false
    } = body;

    // Validate that we have either datasets or PDFs selected
    if ((!datasets || datasets.length === 0) && (!pdfs || pdfs.length === 0)) {
      return NextResponse.json({
        success: false,
        error: 'Please select at least one dataset or PDF document before sending a message.'
      }, { status: 400 });
    }

    // Validate that we have a message
    if (!message || typeof message !== 'string' || message.trim() === '') {
      return NextResponse.json({
        success: false,
        error: 'Message is required'
      }, { status: 400 });
    }

    // Create the user message object
    const lastUserMessage = { role: 'user', content: message.trim() };

    // Initialize Pinecone client
    const pinecone = getPineconeClient();
    const indexName = process.env.PINECONE_INDEX || 'adeloop';
    const index = pinecone.index(indexName);

    // Initialize embeddings
    const embeddings = getEmbeddings();

    // Define metadata filter for multiple datasets and PDFs
    let metadataFilter: Record<string, any> | undefined = undefined;
    
    if (!useNamespaceOnly) {
      const filters: any[] = [];
      
      // Add dataset filters
      if (datasets && datasets.length > 0) {
        datasets.forEach(datasetId => {
          filters.push({ source: 'dataset', datasetId: datasetId });
        });
      }
      
      // Add PDF filters
      if (pdfs && pdfs.length > 0) {
        pdfs.forEach(pdfId => {
          filters.push({ source: 'pdf', pdfId: pdfId });
        });
      }
      
      // Combine filters with OR logic if we have multiple sources
      if (filters.length > 1) {
        metadataFilter = { $or: filters };
      } else if (filters.length === 1) {
        metadataFilter = filters[0];
      }
      
      console.log('Using metadata filter:', metadataFilter);
    }
    
    // Create vector store
    const vectorStore = await PineconeStore.fromExistingIndex(embeddings, {
      pineconeIndex: index,
      namespace,
      textKey: 'text',
    });
    
    // Fetch all documents from Pinecone for BM25 indexing
    // This creates a local copy for keyword search
    const fetchedDocs = await vectorStore.similaritySearch(
      "", // Empty query to fetch documents
      100, // Fetch a reasonable number of documents
      metadataFilter
    );

    // Initialize an array to store all documents for BM25 indexing
    const allDocuments = fetchedDocs;

    // metadataFilter is already defined above, no need to redefine

    // Create vector retriever with dynamic k value - increased for better coverage
    const vectorRetriever = vectorStore.asRetriever({
      searchType: 'similarity',
      k: retrievalK || 15, // Increased from 10 to 15 for better retrieval coverage
      filter: metadataFilter
    });
    
    // Create BM25 retriever for keyword search with robust error handling
    // First, create the base retriever with increased parameters
    const baseBM25Retriever = new BM25Retriever({
      docs: allDocuments,
      k: retrievalK || 15, // Increased from 10 to 15 for better keyword coverage
    });
    
    // Create a wrapped BM25 retriever that handles regex escaping
    const bm25Retriever = {
      getRelevantDocuments: async (query: string): Promise<Document[]> => {
        try {
          // Escape special regex characters in the query
          const sanitizedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
          console.log(`Sanitized BM25 query: ${sanitizedQuery}`);
          return await baseBM25Retriever.getRelevantDocuments(sanitizedQuery);
        } catch (error) {
          console.error('Error in BM25 retrieval:', error);
          // Return empty array if BM25 fails - the hybrid search will still have vector results
          return [];
        }
      }
    };
    
    // Function to perform hybrid search with MMR re-ranking (enhanced parameters)
    const hybridSearch = async (query: string, k: number = 10): Promise<Document[]> => {
      // Fetch more documents than needed for better MMR re-ranking
      const fetchK = Math.min(k * 3, 50); // Increased from k*2, 30 to k*3, 50 for broader initial retrieval
      
      try {
        console.log(`Processing query: ${query}`);
        
        // Get results from both retrievers
        // Our wrapped BM25Retriever already handles regex escaping internally
        const [vectorResults, keywordResults] = await Promise.all([
          vectorRetriever.getRelevantDocuments(query),
          bm25Retriever.getRelevantDocuments(query)
        ]);
      
        // Create a map to deduplicate documents
        const uniqueDocsMap = new Map<string, ScoredDocument>();
        
        // Score vector results (higher weight for semantic search)
        vectorResults.forEach((doc, i) => {
          const key = `${doc.metadata.source}-${doc.metadata.datasetId || ''}-${doc.pageContent.substring(0, 50)}`;
          uniqueDocsMap.set(key, { 
            doc, 
            score: (vectorResults.length - i) * 1.5 // Weight vector results higher
          });
        });
        
        // Score keyword results
        keywordResults.forEach((doc, i) => {
          const key = `${doc.metadata.source}-${doc.metadata.datasetId || ''}-${doc.pageContent.substring(0, 50)}`;
          if (uniqueDocsMap.has(key)) {
            // Add to existing score if document already exists
            const existingDoc = uniqueDocsMap.get(key)!;
            existingDoc.score += (keywordResults.length - i);
          } else {
            uniqueDocsMap.set(key, { 
              doc, 
              score: keywordResults.length - i 
            });
          }
        });
        
        // Convert map to array and sort by score
        const scoredDocs = Array.from(uniqueDocsMap.values())
          .sort((a, b) => b.score - a.score)
          .slice(0, fetchK);
        
        // Apply MMR re-ranking for diversity
        return applyMMR(scoredDocs.map(item => item.doc), query, k);
      } catch (error) {
        console.error('Error in hybrid search:', error);
        
        // First fallback: vector search only
        console.log('Falling back to vector search only due to error');
        let fallbackResults = await vectorRetriever.getRelevantDocuments(query);
        
        // Second fallback: Try a simpler query if results are insufficient
        if (fallbackResults.length < 3) {
          console.log('Insufficient results, trying keyword-only fallback');
          // Extract main keywords and use those directly
          const keywords = query.toLowerCase()
            .split(/\s+/)
            .filter(w => w.length > 3)
            .slice(0, 3);
          
          if (keywords.length > 0) {
            const keywordQuery = keywords.join(' ');
            console.log('Keyword fallback query:', keywordQuery);
            const keywordResults = await vectorRetriever.getRelevantDocuments(keywordQuery);
            
            // Combine results, remove duplicates
            const allResults = [...fallbackResults, ...keywordResults];
            const uniqueResults = Array.from(
              new Map(allResults.map(doc => [
                `${doc.metadata.source}-${doc.pageContent.substring(0, 50)}`, 
                doc
              ])).values()
            );
            
            fallbackResults = uniqueResults;
          }
        }
        
        // Apply metadata enhancement even in fallback scenario
        const enhancedFallbackResults = await enhanceRetrievalWithMetadata(query, fallbackResults, k);
        return enhancedFallbackResults.slice(0, k);
      }
    };
    
    // Enhanced query processing to improve retrieval effectiveness
    const enhanceQueryForRetrieval = (query: string): string => {
      try {
        // Preserve the original query structure but enhance for better retrieval
        let enhancedQuery = query.trim();

        // Extract and preserve important entities
        const entities = {
          ids: query.match(/\b([A-Z0-9]{4,})\b/g) || [],
          numbers: query.match(/\b\d+(\.\d+)?\b/g) || [],
          dates: query.match(/\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[-\s]?\d{2,4}\b/gi) || [],
          emails: query.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g) || [],
          currencies: query.match(/\$\d+(\.\d{2})?|\d+\s*(USD|EUR|GBP)/gi) || []
        };

        // Create entity-focused variations for better retrieval
        const entityTerms = [
          ...entities.ids,
          ...entities.numbers,
          ...entities.dates,
          ...entities.emails,
          ...entities.currencies
        ].filter(Boolean);

        // If we have specific entities, create focused search terms
        if (entityTerms.length > 0) {
          // Add entity terms to enhance retrieval without losing context
          enhancedQuery = `${enhancedQuery} ${entityTerms.join(' ')}`;
        }

        // Extract key business terms and concepts
        const businessTerms = query.match(/\b(employee|salary|department|project|revenue|cost|profit|budget|performance|target|goal|metric|kpi|analysis|report|data|trend|growth|decline|increase|decrease)\b/gi) || [];

        if (businessTerms.length > 0) {
          // Add relevant business context terms
          enhancedQuery = `${enhancedQuery} ${businessTerms.join(' ')}`;
        }

        return enhancedQuery.trim();
      } catch (error) {
        console.error('Error in query enhancement:', error);
        return query; // Return original query if enhancement fails
      }
    };
    
    // Metadata-enhanced retrieval for specific values in the query
    const enhanceRetrievalWithMetadata = async (
      query: string, 
      baseResults: Document[],
      k: number
    ): Promise<Document[]> => {
      try {
        // If we already have enough results, return them
        if (baseResults.length >= k) return baseResults;
        
        // Try to extract potential metadata values from the query
        const potentialIds = query.match(/\b([A-Z0-9]{4,})\b/g) || [];
        const potentialDates = query.match(/\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[-\s]?\d{2,4}\b/gi) || [];
        const potentialNumbers = query.match(/\b\d+(\.\d+)?\b/g) || [];
        
        // If we have potential metadata values, try targeted retrieval
        if (potentialIds.length > 0 || potentialDates.length > 0 || potentialNumbers.length > 0) {
          console.log('Found potential metadata values for enhanced retrieval:', { 
            potentialIds, potentialDates, potentialNumbers 
          });
          
          // Create targeted search promises
          const metadataQueries = [];
          
          // Try each specific value as a standalone query
          const specificValues = [...potentialIds, ...potentialDates, ...potentialNumbers];
          for (const value of specificValues) {
            metadataQueries.push(vectorRetriever.getRelevantDocuments(value));
          }
          
          // Execute metadata queries
          if (metadataQueries.length > 0) {
            const metadataResults = (await Promise.all(metadataQueries)).flat();
            console.log(`Retrieved ${metadataResults.length} additional documents using metadata values`);
            
            // Combine with base results, deduplicating
            const allDocs = [...baseResults, ...metadataResults];
            const uniqueDocs = Array.from(
              new Map(allDocs.map(doc => [
                `${doc.metadata.source}-${doc.pageContent.substring(0, 50)}`, 
                doc
              ])).values()
            );
            
            return uniqueDocs.slice(0, k);
          }
        }
        
        return baseResults;
      } catch (error) {
        console.error('Error in metadata-enhanced retrieval:', error);
        return baseResults; // Return original results if enhancement fails
      }
    };
    
    // Maximal Marginal Relevance re-ranking
    // This helps reduce redundancy while maintaining relevance
    const applyMMR = (docs: Document[], query: string, k: number, lambda: number = 0.6): Document[] => {
      if (docs.length <= 1) return docs;
      
      // Simple implementation of MMR for diversity
      const selected: Document[] = [];
      const remaining = [...docs];
      
      // Helper to calculate similarity (robust implementation)
      const similarity = (a: string, b: string): number => {
        try {
          // Ensure inputs are strings
          const textA = typeof a === 'string' ? a : String(a || '');
          const textB = typeof b === 'string' ? b : String(b || '');
          
          // Sanitize inputs first with additional safety
          const sanitize = (text: string): string => {
            try {
              return text
                .toLowerCase()
                // Replace any non-alphanumeric characters with space
                .replace(/[^a-z0-9\s]/g, ' ')
                // Replace multiple spaces with single space (ensure there's a character before +)
                .replace(/\s+/g, ' ')
                .trim();
            } catch (e) {
              console.error('Error in sanitize function:', e);
              // Return a safe fallback if regex fails
              return text.toLowerCase().trim();
            }
          };

          // Sanitize and split into words
          const aWords = new Set(sanitize(textA).split(' ').filter(Boolean));
          const bWords = new Set(sanitize(textB).split(' ').filter(Boolean));

          if (aWords.size === 0 || bWords.size === 0) return 0;

          let intersection = 0;
          aWords.forEach(word => {
            if (bWords.has(word)) intersection++;
          });
          
          return intersection / (aWords.size + bWords.size - intersection);
        } catch (error) {
          console.error('Error calculating similarity:', error);
          return 0; // Safe fallback
        }
      };
      
      while (selected.length < k && remaining.length > 0) {
        let nextBestScore = -Infinity;
        let nextBestIndex = -1;
        
        for (let i = 0; i < remaining.length; i++) {
          const doc = remaining[i];
          
          // Calculate relevance to query
          const relevance = similarity(doc.pageContent, query);
          
          // Calculate diversity penalty
          let diversityPenalty = 0;
          for (const selectedDoc of selected) {
            diversityPenalty = Math.max(
              diversityPenalty,
              similarity(doc.pageContent, selectedDoc.pageContent)
            );
          }
          
          // MMR score: lambda * relevance - (1 - lambda) * diversity_penalty
          const score = lambda * relevance - (1 - lambda) * diversityPenalty;
          
          if (score > nextBestScore) {
            nextBestScore = score;
            nextBestIndex = i;
          }
        }
        
        if (nextBestIndex !== -1) {
          selected.push(remaining[nextBestIndex]);
          remaining.splice(nextBestIndex, 1);
        } else {
          break;
        }
      }
      
      return selected;
    };
    
    // Use the hybrid search as our retriever
    const retriever: HybridRetriever = {
      getRelevantDocuments: async (query: string): Promise<Document[]> => await hybridSearch(query, 10)
    };

    // Log the retriever configuration with more details
    console.log('Retriever configuration:', {
      searchType: "similarity",
      k: retrievalK || 10,
      filter: metadataFilter,
      namespace,
      datasets,
      pdfs,
      useNamespaceOnly,
      deepResearch,
      maxIterations,
      searchStrategy
    });

    // Log the Pinecone query parameters for debugging
    console.log('Pinecone query parameters:', {
      namespace: namespace,
      topK: retrievalK || 10,
      filter: metadataFilter
    });

    // Get LLM
    const llm = getLLM(model);

    // Create enhanced prompt template with better accuracy and source attribution
    const systemTemplate =
      `You are an expert AI research assistant with advanced analytical capabilities. Your role is to provide accurate, well-sourced answers based exclusively on the provided context.

      CORE PRINCIPLES:
      - ACCURACY FIRST: Only use information explicitly stated in the provided context
      - COMPREHENSIVE ANALYSIS: Consider all relevant information before responding
      - CLEAR SOURCING: Always cite specific sources for every claim
      - HONEST LIMITATIONS: Clearly state when information is insufficient

      RESPONSE METHODOLOGY:
      1. ANALYZE: Carefully examine all provided context for relevant information
      2. SYNTHESIZE: Combine information from multiple sources when appropriate
      3. CITE: Reference specific sources for every piece of information
      4. VERIFY: Double-check that all claims are supported by the context

      SOURCING REQUIREMENTS:
      - For datasets: "According to [Dataset Name], row [X]..." or "Based on the data in [Dataset Name]..."
      - For PDFs: "As stated in [Document Name], page [X]..." or "According to [Document Name]..."
      - For calculations: Show your work and cite the source data used
      - For comparisons: Reference all sources being compared

      DATA ANALYSIS GUIDELINES:
      - For numerical queries: Provide exact values with proper calculations
      - For ID/key searches: Search thoroughly across all relevant data
      - For trends/patterns: Only identify patterns explicitly supported by the data
      - For missing data: Clearly state "No data found for [specific query]"

      ACCURACY ENHANCEMENTS:
      - Cross-reference information across multiple sources when available
      - Highlight any contradictions or inconsistencies in the data
      - Distinguish between facts, interpretations, and limitations
      - Provide confidence levels when appropriate (e.g., "Based on limited data...")

      RESPONSE STRUCTURE:
      1. **Direct Answer**: Clear, concise response to the question
      2. **Supporting Evidence**: Detailed citations and relevant data
      3. **Analysis**: Interpretation and context (when appropriate)
      4. **Limitations**: Any gaps or uncertainties in the available data

      If information is not available in the context, respond with:
      "I don't have sufficient information in the provided data to answer this question completely. The available data covers [briefly describe what is available], but lacks [specific missing information needed]."

      Context:
      {context}`;

    const promptTemplate = ChatPromptTemplate.fromMessages([
      SystemMessagePromptTemplate.fromTemplate(systemTemplate),
      HumanMessagePromptTemplate.fromTemplate("{question}"),
    ]);

    // Retrieve documents first - using the invoke method to avoid deprecation warning
    console.log('Retrieving documents for query:', lastUserMessage.content);

    // Process the query to enhance retrieval
    const userQuery = lastUserMessage.content.trim();
    
    // Enhance the query for better retrieval
    const enhancedUserQuery = enhanceQueryForRetrieval(userQuery);
    console.log('Enhanced query for retrieval:', enhancedUserQuery);
    
    // Safely extract potential keywords from the enhanced query with proper error handling
    let keywords: string[] = [];
    try {
      // First, create a safe version of the query without regex special characters
      const safeQuery = enhancedUserQuery.toLowerCase();
      
      // Extract keywords more robustly
      keywords = safeQuery
        // Remove special characters but keep alphanumeric and whitespace
        // Use a safer approach to replace special characters
        .replace(/[^a-z0-9\s]/g, ' ')
        // Replace multiple spaces with single space
        .replace(/\s+/g, ' ')
        .trim()
        // Split into words
        .split(' ')
        // Filter out common words and short words
        .filter((word: string) => {
          if (!word || typeof word !== 'string') return false;
          
          const commonWords = new Set([
            'what', 'when', 'where', 'which', 'who', 'why', 'how',
            'the', 'and', 'for', 'with', 'this', 'that', 'from',
            'to', 'in', 'on', 'at', 'by', 'of', 'about', 'is', 'are',
            'sum', 'total', 'find', 'get', 'show', 'tell', 'give'
          ]);
          return word.length > 3 && !commonWords.has(word);
        });
    } catch (error) {
      console.error('Error extracting keywords:', error);
      // Fallback to a simple extraction
      keywords = userQuery
        .toLowerCase()
        .split(/\s+/)
        .filter(w => w.length > 3);
    }
    
    console.log('Extracted keywords:', keywords);
    
    // Implement query expansion for better retrieval with robust error handling
    const expandQuery = async (query: string): Promise<string[]> => {
      // Safety check input
      if (!query || typeof query !== 'string') {
        console.error('Invalid query input to expandQuery');
        return [query || ''];
      }
      
      try {
        // For query expansion, we'll create multiple variations with different focuses
        const baseKeywords = keywords.slice(0, 3); // Take top 3 keywords
        const expandedQueries = [query]; // Always include original query
        
        if (baseKeywords.length > 0) {
          try {
            // Create specific queries focusing on key terms
            if (baseKeywords.length >= 2) {
              const safeKeywords = baseKeywords.slice(0, 2).map(k => k.trim()).filter(Boolean);
              if (safeKeywords.length >= 2) {
                expandedQueries.push(`Find information about ${safeKeywords.join(" and ")} in the documents`);
              }
            }
            
            // Add individual keyword queries
            baseKeywords.forEach(keyword => {
              if (keyword && keyword.trim()) {
                expandedQueries.push(`What information is available about ${keyword.trim()}?`);
              }
            });
            
            // Create combinations if we have multiple keywords
            if (baseKeywords.length >= 2) {
              for (let i = 0; i < baseKeywords.length - 1; i++) {
                for (let j = i + 1; j < baseKeywords.length; j++) {
                  const kw1 = baseKeywords[i]?.trim();
                  const kw2 = baseKeywords[j]?.trim();
                  if (kw1 && kw2) {
                    expandedQueries.push(`Tell me about ${kw1} and ${kw2}`);
                  }
                }
              }
            }
          } catch (error) {
            console.error('Error in query expansion keyword processing:', error);
            // Continue with the original query
            return [query];
          }
        }
        
        // Remove duplicates and empty queries
        // @ts-ignore
        const validQueries = [...new Set(expandedQueries)].filter(q => q && typeof q === 'string' && q.trim() !== '');
        return validQueries.length > 0 ? validQueries : [query]; // Ensure we return at least the original query
      } catch (error) {
        console.error('Error in query expansion:', error);
        // Fallback to just using the original query
        return [query];
      }
    };
    
    // Generate expanded queries with error handling
    let expandedQueries: string[] = [];
    try {
      expandedQueries = await expandQuery(enhancedUserQuery);
      console.log('Expanded queries:', expandedQueries);
    } catch (error) {
      console.error('Error generating expanded queries:', error);
      expandedQueries = [userQuery]; // Fallback to original query
    }
    
    // Retrieve documents for each expanded query with robust error handling
    let retrievedDocs: Document[] = [];
    
    try {
      // Create retrieval promises with individual error handling for each query
      const retrievalPromises = expandedQueries.map(expandedQuery => {
        return (async () => {
          try {
            // Ensure query is safe before passing to retriever
            if (!expandedQuery || typeof expandedQuery !== 'string') {
              console.warn('Skipping invalid query in document retrieval');
              return [] as Document[];
            }
            
            // The wrapped BM25Retriever (retriever) already handles regex escaping internally
            const rawResults = await retriever.getRelevantDocuments(expandedQuery);
            
            // Apply metadata enhancement for each expanded query
            if (rawResults.length < 5) {
              return await enhanceRetrievalWithMetadata(expandedQuery, rawResults, 10);
            }
            return rawResults;
          } catch (error) {
            console.error(`Error retrieving documents for query: "${expandedQuery}":`, error);
            return [] as Document[]; // Return empty array if this specific query fails
          }
        })();
      });
      
      // Wait for all retrievals to complete
      const retrievalResults = await Promise.all(retrievalPromises);
      
      // Flatten and deduplicate results safely
      const seenDocKeys = new Set<string>();
      
      for (const resultSet of retrievalResults) {
        for (const doc of resultSet) {
          try {
            if (!doc || !doc.pageContent) continue;
            
            // Safely create document key with null checks
            const source = doc.metadata?.source || 'unknown';
            const datasetId = doc.metadata?.datasetId || '';
            const contentPreview = typeof doc.pageContent === 'string' ? 
              doc.pageContent.substring(0, 50) : 
              String(doc.pageContent || '').substring(0, 50);
              
            const docKey = `${source}-${datasetId}-${contentPreview}`;
            
            if (!seenDocKeys.has(docKey)) {
              seenDocKeys.add(docKey);
              retrievedDocs.push(doc);
            }
          } catch (error) {
            console.error('Error processing document for deduplication:', error);
            // Skip this document and continue processing
          }
        }
      }
      
      console.log(`Retrieved ${retrievedDocs.length} documents from vector store after query expansion`);
    } catch (error) {
      console.error('Error in document retrieval process:', error);
      // If the whole retrieval process fails, try a simpler approach
      try {
        console.log('Attempting fallback retrieval with vector search only...');
        retrievedDocs = await vectorRetriever.getRelevantDocuments(userQuery);
      } catch (secondError) {
        console.error('Even fallback retrieval failed:', secondError);
        retrievedDocs = []; // Last resort fallback to empty array
      }
    }
    
    // Log the first few documents for debugging
    if (retrievedDocs.length > 0) {
      console.log('Sample documents:',
        retrievedDocs.slice(0, 2).map(doc => ({
          metadata: doc.metadata,
          contentPreview: doc.pageContent.substring(0, 100) + '...'
        }))
      );
    }
    
    // Process documents to ensure they are strings
    const processedDocs = retrievedDocs.map(doc => {
      const docCopy = { ...doc };
      if (typeof docCopy.pageContent !== 'string') {
        docCopy.pageContent = JSON.stringify(docCopy.pageContent);
      }
      return docCopy;
    });
    // Check if we have any documents
    if (processedDocs.length === 0) {
      console.log('No relevant documents found for query:', lastUserMessage.content);

      // Provide more specific feedback based on the selected sources
      let noDocsMessage = "I couldn't find any relevant information in the selected data sources to answer your question. ";

      if (datasets && datasets.length > 0) {
        noDocsMessage += `The selected dataset${datasets.length > 1 ? 's' : ''} might not contain information related to your question. `;
      }
      if (pdfs && pdfs.length > 0) {
        noDocsMessage += `The selected PDF${pdfs.length > 1 ? 's' : ''} might not contain information related to your question. `;
      }

      noDocsMessage += "Please try a different question or check if your data contains the information you're looking for.";

      return NextResponse.json({
        success: false,
        content: noDocsMessage,
        model: model,
        provider: getModelProvider(model),
        sourceDocuments: []
      });
    }

    // Format documents as a string with detailed source information
    const formattedDocsText = processedDocs.map((doc, i) => {
      const source = doc.metadata?.source || 'unknown';
      let sourceInfo = '';

      if (source === 'pdf') {
        sourceInfo = `PDF: ${doc.metadata?.fileName || 'Unknown'} (Page ${doc.metadata?.page || 'N/A'})`;
      } else if (source === 'dataset') {
        sourceInfo = `Dataset: ${doc.metadata?.datasetName || 'Unknown'} (Row ${doc.metadata?.rowIndex !== undefined ? doc.metadata.rowIndex + 1 : 'N/A'})`;
      } else {
        sourceInfo = `Source: ${source}`;
      }

      // Add metadata debug info
      console.log(`Document ${i+1} metadata:`, doc.metadata);

      return `Document ${i+1} [${sourceInfo}]: ${doc.pageContent}`;
    }).join('\n\n');

    // Log the formatted context for debugging
    console.log('Formatted context length:', formattedDocsText.length);
    console.log('Formatted context preview:', formattedDocsText.substring(0, 200) + '...');

    // Create improved prompt template with better handling of partial information
    const contextText = processedDocs.map(doc => {
      // Include source attribution for better transparency
      let source = '';
      if (doc.metadata.source) {
        source = doc.metadata.source;
        if (doc.metadata.datasetId) {
          source += ` (${doc.metadata.datasetId})`;
        }
        if (doc.metadata.page) {
          source += ` page ${doc.metadata.page}`;
        }
      }
      
      return `Source: ${source}\n${doc.pageContent}\n`;
    }).join('\n');
    
    // Final prompt construction with enhanced accuracy and analysis
    const prompt = `You are an expert AI research assistant with advanced analytical capabilities. Your task is to provide accurate, comprehensive answers based exclusively on the provided documents.

ANALYSIS FRAMEWORK:
1. **Comprehensive Review**: Examine ALL provided documents for relevant information
2. **Accurate Extraction**: Extract only factual information explicitly stated in the documents
3. **Source Attribution**: Cite specific sources for every piece of information
4. **Synthesis**: Combine information from multiple sources when appropriate
5. **Limitation Acknowledgment**: Clearly state when information is insufficient

RESPONSE REQUIREMENTS:
- Use ONLY information from the provided documents
- Cite specific sources for every claim (document name, page number, row number, etc.)
- For numerical data: Provide exact values and show calculations if needed
- For missing information: Clearly state what is not available
- For partial information: Explain what is known and what is missing

QUALITY STANDARDS:
- Accuracy: Every statement must be verifiable in the source documents
- Completeness: Address all aspects of the question that can be answered
- Clarity: Use clear, professional language with proper structure
- Transparency: Distinguish between facts, interpretations, and limitations

If the documents don't contain sufficient information to answer the question:
"Based on my analysis of the provided documents, I don't have sufficient information to answer this question completely. The available data includes [brief description of what is available], but lacks [specific information needed to fully answer the question]."

For partial answers:
"Based on the available information in the documents, I can provide the following insights: [answer with sources], however, [explain limitations or missing information]."

DOCUMENTS:
${contextText}

USER QUESTION: ${lastUserMessage.content}

ANALYSIS AND ANSWER:`;
    
    // Store the documents for reference in the response
    const sourceDocuments = processedDocs.map((doc, i) => ({
      content: doc.pageContent,
      metadata: doc.metadata,
      index: i + 1
    }));

    // Execute LLM directly with the formatted prompt
    console.log('Sending prompt to LLM:', {
      model: model,
      provider: getModelProvider(model),
      promptLength: prompt.length,
      promptPreview: prompt.substring(0, 200) + '...'
    });

    // Call the LLM
    const llmResponse = await llm.invoke(prompt);

    console.log('Raw LLM response:', JSON.stringify(llmResponse, null, 2));

    // Extract the text content in a type-safe way
    let response = '';

    try {
      // Use a safer approach to extract text from the response
      const extractText = async () => {
        // Try to extract content directly from AIMessage
        try {
          // For AIMessage objects, try to get content directly
          if (llmResponse && typeof llmResponse === 'object' && 'content' in llmResponse) {
            const content = llmResponse.content;
            if (typeof content === 'string' && content.trim() !== '') {
              return content;
            }
          }
        } catch (e) {
          console.log('Direct content extraction failed:', e);
        }

        // If that fails, try to extract text based on common response formats
        const responseObj = llmResponse as any; // Use any type for flexible property access

        // Try different properties where text content might be found
        if (responseObj) {
          // Check for direct content
          if (responseObj.content && typeof responseObj.content === 'string') {
            return responseObj.content;
          }

          // Check for text property (used in some models)
          if (responseObj.text && typeof responseObj.text === 'string') {
            return responseObj.text;
          }

          // Check for message.content pattern
          if (responseObj.message && typeof responseObj.message === 'object') {
            if (responseObj.message.content && typeof responseObj.message.content === 'string') {
              return responseObj.message.content;
            }
          }

          // Check for choices array (OpenAI-like format)
          if (responseObj.choices && Array.isArray(responseObj.choices) && responseObj.choices.length > 0) {
            const choice = responseObj.choices[0];
            if (choice) {
              if (choice.text && typeof choice.text === 'string') {
                return choice.text;
              }
              if (choice.message && typeof choice.message === 'object' &&
                  choice.message.content && typeof choice.message.content === 'string') {
                return choice.message.content;
              }
            }
          }

          // Check for generations array (LangChain format)
          if (responseObj.generations && Array.isArray(responseObj.generations) && responseObj.generations.length > 0) {
            const generation = responseObj.generations[0];
            if (generation) {
              if (generation.text && typeof generation.text === 'string') {
                return generation.text;
              }
              if (generation.message && typeof generation.message === 'object' &&
                  generation.message.content && typeof generation.message.content === 'string') {
                return generation.message.content;
              }
            }
          }
        }

        // If we can't find text in a structured way, convert the whole object to string
        return JSON.stringify(responseObj);
      };

      // Get the text content
      response = await extractText();
    } catch (parseError) {
      console.error('Error parsing LLM response:', parseError);
      // Fallback to string conversion of the whole object
      response = typeof llmResponse === 'string'
        ? llmResponse
        : JSON.stringify(llmResponse);
    }

    // Ensure we have a non-empty response
    if (!response || response.trim() === '' || response === '{}') {
      response = "I couldn't generate a proper response. Please try again with a different question.";
    }

    return NextResponse.json({
      success: true,
      content: response,
      model: model,
      provider: getModelProvider(model),
      sourceDocuments: sourceDocuments
    });
  } catch (error: any) {
    console.error('Error in LangChain RAG chat API:', error);
    return NextResponse.json({ success: false, error: `Internal server error: ${error.message}` }, { status: 500 });
  }
}
