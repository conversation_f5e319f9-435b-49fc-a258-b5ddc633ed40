"use client"

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  FileText, 
  Upload, 
  Trash2, 
  Loader2, 
  CheckCircle, 
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface PDFDocument {
  id: string;
  name: string;
  fileName?: string;
  fileSize?: number;
  pageCount?: number;
  isEmbedded?: boolean;
  createdAt?: string;
}

interface CompactPDFSelectorProps {
  selectedPDFs: string[];
  onSelectPDFs: (pdfs: string[]) => void;
  onEmbedPDF: (pdfId: string) => Promise<boolean>;
  onDeleteEmbedding: (pdfId: string) => Promise<boolean>;
  isEmbedding: boolean;
}

const CompactPDFSelector: React.FC<CompactPDFSelectorProps> = ({
  selectedPDFs,
  onSelectPDFs,
  onEmbedPDF,
  onDeleteEmbedding,
  isEmbedding
}) => {
  const [pdfs, setPdfs] = useState<PDFDocument[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch PDFs from API
  const fetchPDFs = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/pdf-documents', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch PDFs: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success && Array.isArray(data.documents)) {
        setPdfs(data.documents);
      } else {
        throw new Error(data.error || 'Invalid response format');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch PDFs');
      console.error('Error fetching PDFs:', err);
      toast.error(`Error fetching PDFs: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPDFs();
  }, []);

  const handlePDFToggle = (pdfId: string) => {
    if (selectedPDFs.includes(pdfId)) {
      onSelectPDFs(selectedPDFs.filter(id => id !== pdfId));
    } else {
      onSelectPDFs([...selectedPDFs, pdfId]);
    }
  };

  const handleEmbedClick = async (pdfId: string) => {
    const success = await onEmbedPDF(pdfId);
    if (success) {
      fetchPDFs(); // Refresh to update embedding status
    }
  };

  const handleDeleteEmbedding = async (pdfId: string) => {
    const success = await onDeleteEmbedding(pdfId);
    if (success) {
      fetchPDFs(); // Refresh to update embedding status
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)}MB`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-4">
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        <span className="text-sm text-muted-foreground">Loading PDFs...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center gap-2 py-2 text-sm text-red-600">
        <AlertCircle className="h-4 w-4" />
        <span>{error}</span>
      </div>
    );
  }

  if (pdfs.length === 0) {
    return (
      <div className="text-center py-4 text-sm text-muted-foreground">
        <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
        <p>No PDF documents available</p>
        <p className="text-xs">Upload PDFs to get started</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium flex items-center gap-1">
          <FileText className="h-3 w-3" />
          PDF Documents
        </h4>
        <Badge variant="outline" className="text-xs">
          {pdfs.length}
        </Badge>
      </div>
      
      <ScrollArea className="max-h-48">
        <div className="space-y-1">
          {pdfs.map((pdf) => (
            <div
              key={pdf.id}
              className={cn(
                "flex items-center gap-2 p-2 rounded-md border transition-colors",
                selectedPDFs.includes(pdf.id)
                  ? "bg-primary/5 border-primary/20"
                  : "hover:bg-muted/50"
              )}
            >
              <Checkbox
                checked={selectedPDFs.includes(pdf.id)}
                onCheckedChange={() => handlePDFToggle(pdf.id)}
                className="h-3 w-3"
              />
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium truncate">
                    {pdf.name || pdf.fileName}
                  </span>
                  {pdf.isEmbedded && (
                    <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                  )}
                </div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  {pdf.pageCount && <span>{pdf.pageCount} pages</span>}
                  {pdf.fileSize && <span>{formatFileSize(pdf.fileSize)}</span>}
                </div>
              </div>
              
              <div className="flex items-center gap-1">
                {pdf.isEmbedded ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteEmbedding(pdf.id)}
                    disabled={isEmbedding}
                    className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEmbedClick(pdf.id)}
                    disabled={isEmbedding}
                    className="h-6 w-6 p-0 text-blue-500 hover:text-blue-700"
                  >
                    {isEmbedding ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <Upload className="h-3 w-3" />
                    )}
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
      
      {selectedPDFs.length > 0 && (
        <div className="pt-1">
          <Badge variant="secondary" className="text-xs">
            {selectedPDFs.length} selected
          </Badge>
        </div>
      )}
    </div>
  );
};

export default CompactPDFSelector;
